<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Case_AccountId2</name>
        <label>Assign Case.AccountId</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>$Record.AccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_ServiceContract.ParentServiceContract.AccountId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Case_DriverId_c</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Case_AccountId3</name>
        <label>Assign Case.AccountId</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>$Record.AccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_SC_related_to_CA.ParentServiceContract.Account.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Case_DriverId_c2</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Case_ContactId_Cliente</name>
        <label>Assign Case.ContactId Cliente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>$Record.ContactId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_ServiceContract.ParentServiceContract.Account.PersonContactId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_TargaVeicoloCanali</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Case_ContactId_Cliente2</name>
        <label>Assign Case.ContactId Cliente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>$Record.ContactId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_SC_related_to_CA.ParentServiceContract.Account.PersonContactId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>GetAccountDetailsInfo</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Case_ContactId_FM</name>
        <label>Assign Case.ContactId FM</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>$Record.ContactId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>getContactFM.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_TargaVeicoloCanali</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Case_ContactId_FM2</name>
        <label>Assign Case.ContactId FM</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>$Record.ContactId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>getContactFM2.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>GetAccountDetailsInfo</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Case_ContactId_Util</name>
        <label>Assign Case.ContactId Util</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>$Record.ContactId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_ServiceContract.Account.PersonContactId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_TargaVeicoloCanali</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Case_ContactId_Util2</name>
        <label>Assign Case.ContactId Util</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>$Record.ContactId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_SC_related_to_CA.Account.PersonContactId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>GetAccountDetailsInfo</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Case_ContractAsset_c</name>
        <label>Assign Case.ContractAsset__c</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>$Record.ContractAsset__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_ContractAsset.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>GetAccountDetailsInfo</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Case_ContractAsset_c3</name>
        <label>Assign Case.ContractAsset__c3</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>$Record.ContractAsset__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_ContractAsset_with_Targa.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>SCId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_ContractAsset_with_Targa.ServiceContract__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_SC_related_to_CA</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Case_DriverId_c</name>
        <label>Assign Case.DriverId__c</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>$Record.DriverId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_ServiceContract.Account.PersonContactId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Ent_Chiamante_or_is_Case_PQ</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Case_DriverId_c2</name>
        <label>Assign Case.DriverId__c</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>$Record.DriverId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_SC_related_to_CA.Account.PersonContactId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Ent_Chiamante2</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Case_Info</name>
        <label>Assign Case Info</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>$Record.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>getUfficioGroupId.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>$Record.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Nuova richiesta</stringValue>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <name>Assign_Case_Info2</name>
        <label>Assign Case Info</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>$Record.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>getUfficioUnipolAssistanceGroupId.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>$Record.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Nuova richiesta</stringValue>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <name>Assign_First_CA</name>
        <label>Assign First CA</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>$Record.ContractAsset__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_CA_results.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>SCId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_CA_results.ServiceContract__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Count_num_CA_records</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>assignEntitlementProcess</name>
        <label>assignEntitlementProcess</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>$Record.EntitlementId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>getEntitlement.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Case_da_canali_esterni</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Clean_account_fields</name>
        <label>Clean account fields</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>$Record.AccountId</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>$Record.ContactId</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <connector>
            <targetReference>Case_da_canali_esterni</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_1_of_Assign_Case_ContactId_Cliente</name>
        <label>Assign Case campi chiamante</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>$Record.NomeChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.Agente__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>$Record.TelChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.TelAgente__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>$Record.EmailChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.EmailAgente__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>$Record.EntChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Agente</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_TargaVeicoloCanali</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_1_of_Assign_Case_ContractAsset_c</name>
        <label>Assign Case.ContractAsset__c</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>$Record.ContractAsset__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_ContractAsset_without_Targa.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>GetAccountDetailsInfo</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Count_num_CA_records</name>
        <label>Count num CA records</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>CA_Counter</assignToReference>
            <operator>Add</operator>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Chech_counter_value</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>isStoppedFalse</name>
        <label>isStoppedFalse</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>$Record.IsStopped</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <name>isStoppedTrue</name>
        <label>isStoppedTrue</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>$Record.IsStopped</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
    </assignments>
    <customErrors>
        <name>Error_Associazione_contratto_targa_non_trovata</name>
        <label>Error: Associazione contratto targa non trovata</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetAccountDetailsInfo</targetReference>
        </connector>
        <customErrorMessages>
            <errorMessage>Associazione contratto targa non trovata</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <customErrors>
        <name>Error_Contrattononvalido</name>
        <label>Error: Contratto non valido</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetAccountDetailsInfo</targetReference>
        </connector>
        <customErrorMessages>
            <errorMessage>Contratto non valido</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <customErrors>
        <name>Error_Targanonvalida</name>
        <label>Error: Targa non valida</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetAccountDetailsInfo</targetReference>
        </connector>
        <customErrorMessages>
            <errorMessage>Targa non valida</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <customErrors>
        <name>Errore_generico</name>
        <label>Errore generico</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <customErrorMessages>
            <errorMessage>Si è verificato un errore: impossibile completare l&apos;operazione.
 
{!$Flow.FaultMessage}</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <customErrors>
        <name>Valorizzare_almeno_uno_tra_contratto_e_targa</name>
        <label>Error: Valorizzare almeno uno tra contratto e targa</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetAccountDetailsInfo</targetReference>
        </connector>
        <customErrorMessages>
            <errorMessage>Valorizzare almeno uno tra contratto e targa</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <decisions>
        <name>BeforeInsertOrUpdate</name>
        <label>BeforeInsertOrUpdate</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>checkAttesaCliente</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Update</defaultConnectorLabel>
        <rules>
            <name>Insert</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Id</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>getAccountSocRental</targetReference>
            </connector>
            <label>Insert</label>
        </rules>
    </decisions>
    <decisions>
        <name>Case_da_canali_esterni</name>
        <label>Case da canali esterni</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>true</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>isCaseAR</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>isCaseSitoWeb</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>isCasePQ</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_SC_Quadro_RectypeId</targetReference>
            </connector>
            <label>true</label>
        </rules>
    </decisions>
    <decisions>
        <name>Chech_counter_value</name>
        <label>Chech counter value</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Loop_CA_results</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>X1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>CA_Counter</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <numberValue>1.0</numberValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>Error_Associazione_contratto_targa_non_trovata</targetReference>
            </connector>
            <label>&gt; 1</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Asset</name>
        <label>Check Asset</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Error_Targanonvalida</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Asset_is_not_null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Asset</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_ContractAsset</targetReference>
            </connector>
            <label>Asset is not null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Asset2</name>
        <label>Check Asset</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Error_Targanonvalida</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Asset_is_not_null2</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Asset2</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_ContractAsset_with_Targa</targetReference>
            </connector>
            <label>Asset is not null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_CA</name>
        <label>Check CA</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Error_Associazione_contratto_targa_non_trovata</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>CA_is_NOT_null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_ContractAsset</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Case_ContractAsset_c</targetReference>
            </connector>
            <label>CA is NOT null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_CA2</name>
        <label>Check CA</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Error_Associazione_contratto_targa_non_trovata</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>CA_is_NOT_null2</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_ContractAsset_without_Targa</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_Assign_Case_ContractAsset_c</targetReference>
            </connector>
            <label>CA is NOT null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_CA3</name>
        <label>Check CA</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Get_ContractAsset_with_Targa_and_a_null</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>CA_is_NOT_null3</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_ContractAsset_with_Targa</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Case_ContractAsset_c3</targetReference>
            </connector>
            <label>CA is NOT null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Case_AccountId</name>
        <label>Check Case.AccountId</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Case_da_canali_esterni</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Case_AccountId_NULL</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.AccountId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>CheckAccountAccountRel_for_Rental</targetReference>
            </connector>
            <label>Case.AccountId != NULL</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Contact_FM</name>
        <label>Check Contact FM</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Check_TargaVeicoloCanali</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Contact_FM_Not_null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getContactFM</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Case_ContactId_FM</targetReference>
            </connector>
            <label>Contact FM Not null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Contact_FM2</name>
        <label>Check Contact FM</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>GetAccountDetailsInfo</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Contact_FM_Not_null2</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getContactFM2</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Case_ContactId_FM2</targetReference>
            </connector>
            <label>Contact FM Not null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Ent_Chiamante2</name>
        <label>Check Ent Chiamante</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>GetAccountDetailsInfo</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Utilizzatore2</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.EntChiamante__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Utilizzatore</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Case_ContactId_Util2</targetReference>
            </connector>
            <label>Utilizzatore</label>
        </rules>
        <rules>
            <name>FM2</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.EntChiamante__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Fleet Manager</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>getContactFM2</targetReference>
            </connector>
            <label>FM</label>
        </rules>
        <rules>
            <name>Cliente2</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.EntChiamante__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Cliente</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Case_ContactId_Cliente2</targetReference>
            </connector>
            <label>Cliente</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Ent_Chiamante_or_is_Case_PQ</name>
        <label>Check Ent Chiamante or is Case PQ</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Check_TargaVeicoloCanali</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Utilizzatore</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.EntChiamante__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Utilizzatore</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Case_ContactId_Util</targetReference>
            </connector>
            <label>Utilizzatore</label>
        </rules>
        <rules>
            <name>FM</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.EntChiamante__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Fleet Manager</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>getContactFM</targetReference>
            </connector>
            <label>FM</label>
        </rules>
        <rules>
            <name>Cliente</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.EntChiamante__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Cliente</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Case_ContactId_Cliente</targetReference>
            </connector>
            <label>Cliente</label>
        </rules>
        <rules>
            <name>Case_PQ</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>isCasePQ</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_Assign_Case_ContactId_Cliente</targetReference>
            </connector>
            <label>Case PQ</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_get_uffici_result</name>
        <label>Check get uffici result</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>getUfficioUnipolAssistanceGroupId</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>exists_config</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getUfficiCanali</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>getUfficiCanali.QueueDevName__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>getUfficioGroupId</targetReference>
            </connector>
            <label>exists config</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_IdContrattoCanali</name>
        <label>Check IdContrattoCanali</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Check_TargaVeicoloCanali2</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>IdContrattoCanali NULL</defaultConnectorLabel>
        <rules>
            <name>IdContrattoCanali_NOT_NULL</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.IdContrattoCanali__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_ServiceContract</targetReference>
            </connector>
            <label>IdContrattoCanali NOT NULL</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Origin</name>
        <label>Check Origin</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Check_Case_AccountId</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Origin = EmailService</defaultConnectorLabel>
        <rules>
            <name>Origin_EmailService</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Origin</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Email Services</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>getEntitlement</targetReference>
            </connector>
            <label>Origin != EmailService</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_SC</name>
        <label>Check SC</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Error_Contrattononvalido</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>esiste_contr_singolo</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_ServiceContract</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Case_AccountId2</targetReference>
            </connector>
            <label>esiste contr singolo</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_TargaVeicoloCanali</name>
        <label>Check TargaVeicoloCanali</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Get_ContractAsset_without_Targa</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>TargaVeicoloCanali NULL</defaultConnectorLabel>
        <rules>
            <name>TargaVeicoloCanali_NOT_NULL</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.TargaVeicoloCanali__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Asset</targetReference>
            </connector>
            <label>TargaVeicoloCanali NOT NULL</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_TargaVeicoloCanali2</name>
        <label>Check TargaVeicoloCanali</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Valorizzare_almeno_uno_tra_contratto_e_targa</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>TargaVeicoloCanali NULL</defaultConnectorLabel>
        <rules>
            <name>TargaVeicoloCanali_NOT_NULL2</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.TargaVeicoloCanali__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Asset2</targetReference>
            </connector>
            <label>TargaVeicoloCanali NOT NULL</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Ufficio_groupId</name>
        <label>Check Ufficio groupId</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>exists_queue</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getUfficioGroupId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Case_Info</targetReference>
            </connector>
            <label>exists queue</label>
        </rules>
    </decisions>
    <decisions>
        <name>CheckAccountAccountRel_for_Rental</name>
        <label>CheckAccountAccountRel for Rental</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Case_da_canali_esterni</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>is_NOT_Rental_Account</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getAccountAccountRel</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Clean_account_fields</targetReference>
            </connector>
            <label>is NOT Rental Account</label>
        </rules>
    </decisions>
    <decisions>
        <name>checkAttesaCliente</name>
        <label>checkAttesaCliente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>isStoppedFalse</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Da Riprendere</defaultConnectorLabel>
        <rules>
            <name>Da_Sospendere</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>In attesa risposta cliente</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>isStoppedTrue</targetReference>
            </connector>
            <label>Da Sospendere</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>endDateEntitlement</name>
        <dataType>Date</dataType>
        <expression>ADDMONTHS(TODAY(),3)</expression>
    </formulas>
    <formulas>
        <name>isCaseAR</name>
        <dataType>Boolean</dataType>
        <expression>IF( {!$Record.RecordType.DeveloperName} ==  &apos;ur_CaseAR&apos;, true, false)</expression>
    </formulas>
    <formulas>
        <name>isCasePQ</name>
        <dataType>Boolean</dataType>
        <expression>IF( {!$Record.RecordType.DeveloperName} ==  &apos;ur_CasePQ&apos;, true, false)</expression>
    </formulas>
    <formulas>
        <name>isCaseSitoWeb</name>
        <dataType>Boolean</dataType>
        <expression>IF( {!$Record.RecordType.DeveloperName} ==  &apos;ur_CaseSitoWeb&apos;, true, false)</expression>
    </formulas>
    <formulas>
        <name>today</name>
        <dataType>Date</dataType>
        <expression>TODAY()</expression>
    </formulas>
    <formulas>
        <name>urcs_CheckRTCases</name>
        <dataType>Boolean</dataType>
        <expression>IF(
OR(
{!$Record.RecordType.DeveloperName} == &apos;ur_CaseCRM&apos;,
{!$Record.RecordType.DeveloperName} ==  &apos;ur_CaseAR&apos;,
{!$Record.RecordType.DeveloperName} == &apos;ur_CasePQ&apos;,
{!$Record.RecordType.DeveloperName} == &apos;ur_CaseSitoWeb&apos;
),true,false
)</expression>
    </formulas>
    <interviewLabel>urcs_RTCaseBeforeIU {!$Flow.CurrentDateTime}</interviewLabel>
    <label>urcs_RTCaseBeforeIU</label>
    <loops>
        <name>Loop_CA_results</name>
        <label>Loop CA results</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>Get_ContractAsset_with_Targa_and_a_null</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_First_CA</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Get_SC_related_to_CA</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Asset</name>
        <label>Get Asset</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Asset</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.TargaVeicoloCanali__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Asset</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Asset2</name>
        <label>Get Asset</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Asset2</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.TargaVeicoloCanali__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Asset</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Contact_FM_RectypeId</name>
        <label>Get Contact FM RectypeId</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_IdContrattoCanali</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ur_FleetManager</stringValue>
            </value>
        </filters>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Contact</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_ContractAsset</name>
        <label>Get ContractAsset</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_CA</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ServiceContract__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_ServiceContract.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>Asset__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Asset.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ContractAsset__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_ContractAsset_with_Targa</name>
        <label>Get ContractAsset with Targa</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_CA3</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Asset__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Asset2.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>tsA__c</field>
            <operator>EqualTo</operator>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ContractAsset__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_ContractAsset_with_Targa_and_a_null</name>
        <label>Get  ContractAsset with Targa and a != null</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_CA_results</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Asset__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Asset2.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>tsA__c</field>
            <operator>NotEqualTo</operator>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <limit>
            <numberValue>2.0</numberValue>
        </limit>
        <object>ContractAsset__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_ContractAsset_without_Targa</name>
        <label>Get ContractAsset without Targa</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_CA2</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ServiceContract__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_ServiceContract.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>VeicoloPrinc__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ContractAsset__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_SC_Quadro_RectypeId</name>
        <label>Get SC Quadro RectypeId</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_SC_Singolo_RectypeId</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ur_Quadro</stringValue>
            </value>
        </filters>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ServiceContract</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_SC_related_to_CA</name>
        <label>Get SC related to CA</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Case_AccountId3</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>SCId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ServiceContract</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_SC_Singolo_RectypeId</name>
        <label>Get SC Singolo RectypeId</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Contact_FM_RectypeId</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ur_Singolo</stringValue>
            </value>
        </filters>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ServiceContract</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_ServiceContract</name>
        <label>Get ServiceContract</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_SC</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.IdContrattoCanali__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_SC_Singolo_RectypeId.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ServiceContract</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getAccountAccountRel</name>
        <label>getAccountAccountRel</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Origin</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>FinServ__Account__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.AccountId</elementReference>
            </value>
        </filters>
        <filters>
            <field>FinServ__RelatedAccount__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>getAccountSocRental.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>FinServ__AccountAccountRelation__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetAccountDetailsInfo</name>
        <label>GetAccountDetailsInfo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>getUfficiCanali</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Relation__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>getAccountAccountRel.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>AccountDetails__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getAccountSocRental</name>
        <label>getAccountSocRental</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>getAccountAccountRel</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ExternalId__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Setup.urcs_GeneralSettings__c.GroupSocRentalName__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getContactFM</name>
        <label>getContactFM</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Contact_FM</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_ServiceContract.ParentServiceContract.Account.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Contact_FM_RectypeId.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Contact</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getContactFM2</name>
        <label>getContactFM2</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Contact_FM2</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_SC_related_to_CA.ParentServiceContract.AccountId</elementReference>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Contact_FM_RectypeId.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Contact</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getEntitlement</name>
        <label>getEntitlement</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>assignEntitlementProcess</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>urcs_CaseEntitlement</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Entitlement</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getUfficiCanali</name>
        <label>getUfficiCanali</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_get_uffici_result</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Origin__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Origin</elementReference>
            </value>
        </filters>
        <filters>
            <field>Categoria__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Categoria__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>SottoCategoria__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.SottoCategoria__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>EntChiamante__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.EntChiamante__c</elementReference>
            </value>
        </filters>
        <filters>
            <field>Top__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>GetAccountDetailsInfo.Top__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>urcs_ConfigUfficiCanali__mdt</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getUfficioGroupId</name>
        <label>getUfficioGroupId</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Ufficio_groupId</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>getUfficiCanali.QueueDevName__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getUfficioUnipolAssistanceGroupId</name>
        <label>getUfficioUnipolAssistanceGroupId</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Case_Info2</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Errore_generico</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Type</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Queue</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>urcs_L1_UnipolAssistance</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>BeforeInsertOrUpdate</targetReference>
        </connector>
        <filterFormula>IF(
OR(
{!$Record.RecordType.DeveloperName} == &apos;ur_CaseCRM&apos;,
{!$Record.RecordType.DeveloperName} ==  &apos;ur_CaseAR&apos;,
{!$Record.RecordType.DeveloperName} == &apos;ur_CasePQ&apos;,
{!$Record.RecordType.DeveloperName} == &apos;ur_CaseSitoWeb&apos;,
{!$Record.RecordType.DeveloperName}  == &apos;ur_CaseES&apos;
),
true,false
)</filterFormula>
        <object>Case</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordBeforeSave</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>CA_Counter</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
        <value>
            <numberValue>0.0</numberValue>
        </value>
    </variables>
    <variables>
        <name>SCId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
