<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Send_Email_di_sollecito</name>
        <label>Send Email di sollecito</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <connector>
            <targetReference>Assign_num_Sollecito</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>UpdateCase</targetReference>
        </faultConnector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>emailAddresses</name>
            <value>
                <elementReference>Loop_solleciti.EmailSollecito__c</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderType</name>
            <value>
                <stringValue>OrgWideEmailAddress</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderAddress</name>
            <value>
                <elementReference>Get_Email.Address</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>useEmailTemplate</name>
            <value>
                <stringValue>True</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailTemplateId</name>
            <value>
                <elementReference>Get_Tempate.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientId</name>
            <value>
                <elementReference>Loop_solleciti.ContactId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>relatedRecordId</name>
            <value>
                <elementReference>Loop_solleciti.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>logEmailOnSend</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <offset>0</offset>
        <versionString>1.0.1</versionString>
    </actionCalls>
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Add_to_list</name>
        <label>Add to list</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>ListToUpdate</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_solleciti</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_solleciti</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Add_to_list_0</name>
        <label>Add to list</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>ListToUpdate</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_cases</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_cases</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_num_Sollecito</name>
        <label>Assign num Sollecito</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>Loop_solleciti.NumSolleciti__c</assignToReference>
            <operator>Add</operator>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Add_to_list</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Change_Status_MancataRispCliente</name>
        <label>Change Status MancataRispCliente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>Loop_cases.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Mancata risposta cliente</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Add_to_list_0</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Collect_RT_Id</name>
        <label>Collect RT Id</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>recTypeIdList</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Case_RT.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Case_RT</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>email_sollecito_null</name>
        <label>email sollecito null?</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Loop_solleciti</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>NO</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_solleciti.EmailSollecito__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>invio_email</targetReference>
            </connector>
            <label>NO</label>
        </rules>
    </decisions>
    <decisions>
        <name>invio_email</name>
        <label>invio email?</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Loop_solleciti</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>SI</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_solleciti.InvioEmailAutomatico__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Send_Email_di_sollecito</targetReference>
            </connector>
            <label>SI</label>
        </rules>
    </decisions>
    <description>Scheduled flow per verificare mancate risposte dei clienti dopo 7 giorni e sollecito dopo 2</description>
    <environments>Default</environments>
    <formulas>
        <name>AddNumSolleciti</name>
        <dataType>Number</dataType>
        <expression>{!Loop_solleciti.NumSolleciti__c} + 1</expression>
        <scale>0</scale>
    </formulas>
    <formulas>
        <name>CurrentDateMinusGiorniMancataRisposta</name>
        <dataType>Date</dataType>
        <expression>TODAY() - {!$Setup.urcs_GeneralSettings__c.GiorniMancataRisposta__c}</expression>
    </formulas>
    <formulas>
        <name>CurrentDateMinusGiorniSollecito</name>
        <dataType>Date</dataType>
        <expression>TODAY() - {!$Setup.urcs_GeneralSettings__c.GiorniSollecito__c}</expression>
    </formulas>
    <interviewLabel>urcs_EmailMessageCheckScheduled {!$Flow.CurrentDateTime}</interviewLabel>
    <label>urcs_EmailMessageScheduled</label>
    <loops>
        <name>Loop_Case_RT</name>
        <label>Loop Case RT</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>Get_Case_RT</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Collect_RT_Id</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>getCaseInAttesaRispCliente</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_cases</name>
        <label>Loop cases</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>getCaseInAttesaRispCliente</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Change_Status_MancataRispCliente</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>getCaseInAttesaRispClienteSollecito</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_solleciti</name>
        <label>Loop solleciti</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>getCaseInAttesaRispClienteSollecito</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>email_sollecito_null</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>UpdateCase</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Case_RT</name>
        <label>Get Case RT</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_Case_RT</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>StartsWith</operator>
            <value>
                <stringValue>ur_</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Email</name>
        <label>Get Email</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Tempate</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DisplayName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Unipol Rental CS No Reply</stringValue>
            </value>
        </filters>
        <filters>
            <field>IsVerified</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>OrgWideEmailAddress</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Tempate</name>
        <label>Get Tempate</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_solleciti</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>StartsWith</operator>
            <value>
                <stringValue>urcs_CaseNotificaSollecito</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>EmailTemplate</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getCaseInAttesaRispCliente</name>
        <label>getCaseInAttesaRispCliente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_cases</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>In attesa risposta cliente</stringValue>
            </value>
        </filters>
        <filters>
            <field>dtInAttesaRispCliente__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>CurrentDateMinusGiorniMancataRisposta</elementReference>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>In</operator>
            <value>
                <elementReference>recTypeIdList</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getCaseInAttesaRispClienteSollecito</name>
        <label>getCaseInAttesaRispClienteSollecito</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Email</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Status</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>In attesa risposta cliente</stringValue>
            </value>
        </filters>
        <filters>
            <field>dtInAttesaRispCliente__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>CurrentDateMinusGiorniSollecito</elementReference>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>In</operator>
            <value>
                <elementReference>recTypeIdList</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>UpdateCase</name>
        <label>UpdateCase</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputReference>ListToUpdate</inputReference>
    </recordUpdates>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Case_RT</targetReference>
        </connector>
        <schedule>
            <frequency>Daily</frequency>
            <startDate>2025-05-30</startDate>
            <startTime>23:00:00.000Z</startTime>
        </schedule>
        <triggerType>Scheduled</triggerType>
    </start>
    <status>Active</status>
    <variables>
        <name>ListToUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>recTypeIdList</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
